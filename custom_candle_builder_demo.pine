//@version=5
indicator("MTF Candle Display", overlay=true, max_lines_count=500, max_labels_count=100)

// Input parameters
mtf_timeframe = input.timeframe("5", "MTF Timeframe", group="Candle Settings")
show_custom_candles = input.bool(true, "Show MTF Candles", group="Display")
show_candle_info = input.bool(true, "Show Candle Info Labels", group="Display")
candle_color_bull = input.color(color.green, "Bullish Candle Color", group="Colors")
candle_color_bear = input.color(color.red, "Bearish Candle Color", group="Colors")
wick_color = input.color(color.white, "Wick Color", group="Colors")
candle_border_color = input.color(color.black, "Candle Border Color", group="Colors")

// Trading hours settings
trading_hours_session = input.session("0930-1600", "Trading Hours", group="Trading Hours")
timezone_input = input.string("America/New_York", "Timezone", options=["America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles", "Europe/London", "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai", "Asia/Kolkata", "Australia/Sydney"], group="Trading Hours")

// Arrays to store candle elements for cleanup
var candle_bodies = array.new<box>()
var candle_wicks = array.new<line>()
var candle_labels = array.new<label>()

// Get MTF data using request.security
// Method 1: Non-repainting (shows completed candles only)
mtf_open_confirmed = request.security(syminfo.tickerid, mtf_timeframe, open[1], lookahead=barmerge.lookahead_off)
mtf_high_confirmed = request.security(syminfo.tickerid, mtf_timeframe, high[1], lookahead=barmerge.lookahead_off)
mtf_low_confirmed = request.security(syminfo.tickerid, mtf_timeframe, low[1], lookahead=barmerge.lookahead_off)
mtf_close_confirmed = request.security(syminfo.tickerid, mtf_timeframe, close[1], lookahead=barmerge.lookahead_off)
mtf_time_confirmed = request.security(syminfo.tickerid, mtf_timeframe, time[1], lookahead=barmerge.lookahead_off)

// Method 2: Real-time (repaints but shows current developing candle)
mtf_open_realtime = request.security(syminfo.tickerid, mtf_timeframe, open, lookahead=barmerge.lookahead_off)
mtf_high_realtime = request.security(syminfo.tickerid, mtf_timeframe, high, lookahead=barmerge.lookahead_off)
mtf_low_realtime = request.security(syminfo.tickerid, mtf_timeframe, low, lookahead=barmerge.lookahead_off)
mtf_close_realtime = request.security(syminfo.tickerid, mtf_timeframe, close, lookahead=barmerge.lookahead_off)
mtf_time_realtime = request.security(syminfo.tickerid, mtf_timeframe, time, lookahead=barmerge.lookahead_off)

// Choose which method to use
use_confirmed_data = input.bool(true, "Use Confirmed Data (No Repaint)", group="Repainting Settings", tooltip="True = No repainting but 1 candle delay. False = Real-time but repaints.")

mtf_open = use_confirmed_data ? mtf_open_confirmed : mtf_open_realtime
mtf_high = use_confirmed_data ? mtf_high_confirmed : mtf_high_realtime
mtf_low = use_confirmed_data ? mtf_low_confirmed : mtf_low_realtime
mtf_close = use_confirmed_data ? mtf_close_confirmed : mtf_close_realtime
mtf_time = use_confirmed_data ? mtf_time_confirmed : mtf_time_realtime

// Detect new MTF period
is_new_mtf_period = ta.change(mtf_time) != 0

// Calculate the number of bars in the MTF period
mtf_duration_ms = timeframe.in_seconds(mtf_timeframe) * 1000
current_tf_duration_ms = timeframe.in_seconds(timeframe.period) * 1000
bars_in_mtf = math.round(mtf_duration_ms / current_tf_duration_ms)

// Function to clean up old candle objects
cleanup_old_candles() =>
    max_candles = 50

    // Clean up old candle bodies
    while array.size(candle_bodies) > max_candles
        old_body = array.shift(candle_bodies)
        if not na(old_body)
            box.delete(old_body)

    // Clean up old wicks
    while array.size(candle_wicks) > max_candles * 2  // 2 wicks per candle max
        old_wick = array.shift(candle_wicks)
        if not na(old_wick)
            line.delete(old_wick)

    // Clean up old labels
    while array.size(candle_labels) > max_candles
        old_label = array.shift(candle_labels)
        if not na(old_label)
            label.delete(old_label)

// Additional condition to prevent repainting on real-time bars
draw_condition = use_confirmed_data ? true : barstate.isconfirmed

// Draw MTF candles
if (draw_condition and show_custom_candles and is_new_mtf_period and
   not na(mtf_open) and not na(mtf_high) and not na(mtf_low) and not na(mtf_close))

    // Determine candle color and direction
    is_bullish = mtf_close > mtf_open
    candle_color = is_bullish ? candle_color_bull : candle_color_bear

    // Get the MTF start time
    mtf_start_time = use_confirmed_data ? mtf_time_confirmed : mtf_time_realtime

    // Calculate MTF end time
    mtf_duration_ms = timeframe.in_seconds(mtf_timeframe) * 1000
    mtf_end_time = mtf_start_time + mtf_duration_ms

    // Calculate candle body coordinates
    body_top = math.max(mtf_open, mtf_close)
    body_bottom = math.min(mtf_open, mtf_close)

    // Draw candle body using box with time-based positioning
    candle_body = box.new(left=mtf_start_time, top=body_top, right=mtf_end_time, bottom=body_bottom,
                         bgcolor=color.new(candle_color, 20), border_color=color.new(candle_border_color, 0),
                         border_width=1, border_style=line.style_solid, xloc=xloc.bar_time)
    array.push(candle_bodies, candle_body)

    // Calculate wick position (center of candle time-wise)
    wick_time = mtf_start_time + math.floor(mtf_duration_ms / 2)

    // Draw upper wick if exists
    if mtf_high > body_top
        upper_wick = line.new(x1=wick_time, y1=body_top, x2=wick_time, y2=mtf_high,
                             color=color.new(wick_color, 0), width=2, style=line.style_solid, xloc=xloc.bar_time)
        array.push(candle_wicks, upper_wick)

    // Draw lower wick if exists
    if mtf_low < body_bottom
        lower_wick = line.new(x1=wick_time, y1=body_bottom, x2=wick_time, y2=mtf_low,
                             color=color.new(wick_color, 0), width=2, style=line.style_solid, xloc=xloc.bar_time)
        array.push(candle_wicks, lower_wick)

    // Add candle info label if enabled
    if show_candle_info
        price_range = mtf_high - mtf_low
        candle_info = ("MTF " + mtf_timeframe + " Candle\n" +
                       "Time: " + str.format_time(mtf_start_time, "HH:mm", timezone_input) + "\n" +
                       "O: " + str.tostring(mtf_open, "#.##") + "\n" +
                       "H: " + str.tostring(mtf_high, "#.##") + "\n" +
                       "L: " + str.tostring(mtf_low, "#.##") + "\n" +
                       "C: " + str.tostring(mtf_close, "#.##") + "\n" +
                       "Range: " + str.tostring(price_range, "#.##"))

        info_label = label.new(x=wick_time, y=mtf_high + (price_range * 0.02), text=candle_info,
                              style=label.style_label_down, color=color.new(candle_color, 80),
                              textcolor=color.white, size=size.small, xloc=xloc.bar_time)
        array.push(candle_labels, info_label)

    // Clean up old candle objects
    cleanup_old_candles()

// Plot current 1-minute data for reference (semi-transparent)
plot(high, color=color.new(color.blue, 90), linewidth=1, title="1m High")
plot(low, color=color.new(color.blue, 90), linewidth=1, title="1m Low")

// Display current MTF candle info in a table
if barstate.islast and not na(mtf_open)
    var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.new(color.black, 80), border_width=1)

    if not na(info_table)
        table.cell(info_table, 0, 0, "MTF Candle Info", text_color=color.white, bgcolor=color.new(color.blue, 70))
        table.cell(info_table, 1, 0, mtf_timeframe, text_color=color.white, bgcolor=color.new(color.blue, 70))

        table.cell(info_table, 0, 1, "Open", text_color=color.white)
        table.cell(info_table, 1, 1, str.tostring(mtf_open, "#.##"), text_color=color.white)

        table.cell(info_table, 0, 2, "High", text_color=color.white)
        table.cell(info_table, 1, 2, str.tostring(mtf_high, "#.##"), text_color=color.white)

        table.cell(info_table, 0, 3, "Low", text_color=color.white)
        table.cell(info_table, 1, 3, str.tostring(mtf_low, "#.##"), text_color=color.white)

        table.cell(info_table, 0, 4, "Close", text_color=color.white)
        table.cell(info_table, 1, 4, str.tostring(mtf_close, "#.##"), text_color=color.white)

        table.cell(info_table, 0, 5, "Range", text_color=color.white)
        table.cell(info_table, 1, 5, str.tostring(mtf_high - mtf_low, "#.##"), text_color=color.white)
